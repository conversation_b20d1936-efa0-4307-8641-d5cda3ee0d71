package com.turnera.turnera.blockedSlot.application.find;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Month;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindBlockedSlotsForMedicalCenterInMonth {

  private final BlockedSlotService blockedSlotService;

  public Set<BlockedSlot> find(Integer medicalCenterId, Month month) {
    log.info("Finding blocked slots for medical center: {} in month: {}", medicalCenterId, month);
    return blockedSlotService.findByMedicalCenterIdAndMonth(medicalCenterId, month);
  }
}
