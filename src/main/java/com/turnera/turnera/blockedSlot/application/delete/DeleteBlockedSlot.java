package com.turnera.turnera.blockedSlot.application.delete;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class DeleteBlockedSlot {

  private final BlockedSlotService blockedSlotService;

  @Transactional
  public void execute(Integer id) {
    if (!blockedSlotService.existsById(id)) {
      throw new EntityNotFoundException("Blocked slot not found with id: " + id);
    }
    blockedSlotService.deleteById(id);
  }
}
