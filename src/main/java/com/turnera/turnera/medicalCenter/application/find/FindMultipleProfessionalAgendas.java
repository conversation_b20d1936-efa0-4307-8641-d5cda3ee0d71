package com.turnera.turnera.medicalCenter.application.find;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterInMonth;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsForMedicalCenterInMonth;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.medicalCenter.domain.entities.FindProfessionalAgendasInput;
import com.turnera.turnera.medicalCenter.domain.utils.BuildProfessionalAgendaDtoUtil;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindMultipleProfessionalAgendas {

  private final FindAppointmentsForMedicalCenterInMonth findAppointmentsForMedicalCenterInMonth;
  private final FindBlockedSlotsForMedicalCenterInMonth findBlockedSlotsForMedicalCenterInMonth;
  private final BuildProfessionalAgendaDtoUtil buildProfessionalAgendaDtoUtil;

  public Map<Long, ProfessionalAgendaDTO> find(FindProfessionalAgendasInput input) {
    log.info(
        "Finding professional schedules for professionals: {} in medical center {} in month: {}",
        input.getRelationships().size(),
        input.getMedicalCenterId(),
        input.getMonth());
    Set<Long> professionalIds =
        input.getRelationships().stream()
            .map(ProfessionalMedicalCenterRelationship::getProfessionalId)
            .collect(Collectors.toSet());
    Map<Long, List<Appointment>> appointmentsByProfessionalId =
        findAppointmentsForMedicalCenterInMonth
            .find(input.getMedicalCenterId(), input.getMonth())
            .stream()
            .filter(appointment -> professionalIds.contains(appointment.getProfessionalId()))
            .collect(Collectors.groupingBy(Appointment::getProfessionalId));
    Map<Long, List<BlockedSlot>> blockedSlotsByProfessionalId =
        findBlockedSlotsForMedicalCenterInMonth
            .find(input.getMedicalCenterId(), input.getMonth())
            .stream()
            .filter(blockedSlot -> professionalIds.contains(blockedSlot.getProfessionalId()))
            .collect(Collectors.groupingBy(BlockedSlot::getProfessionalId));

    return input.getRelationships().stream()
        .collect(
            Collectors.toMap(
                ProfessionalMedicalCenterRelationship::getProfessionalId,
                relationship ->
                    buildProfessionalAgendaDtoUtil.buildProfessionalScheduleDTO(
                        input.getYear(),
                        input.getMonth(),
                        relationship,
                        appointmentsByProfessionalId.getOrDefault(
                            relationship.getProfessionalId(), List.of()),
                        blockedSlotsByProfessionalId.getOrDefault(
                            relationship.getProfessionalId(), List.of()))));
  }
}
