package com.turnera.turnera.appointment.domain;

import com.turnera.turnera.appointment.domain.entities.AppointmentCreationDetails;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface AppointmentService {

  void migrateOldPatientAppointmentsToNewPatientId(List<Integer> patientIds, Integer newPatientId);

  List<Appointment> findFutureAppointments(
      Integer medicalCenterId, Integer professionalId, LocalDate now);

  List<Appointment> findAppointmentForDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date);

  Appointment createAppointment(AppointmentCreationDetails input);

  List<Appointment> findAppointmentsForMedicalCenterAndConsultationTypeForDate(
      Integer medicalCenterId, Integer consultationTypeId, LocalDate date);

  List<Appointment> findAppointmentsByMedicalCenterIdAndProfessionalIdAndDayOfWeek(
      Integer medicalCenterId, Integer professionalId, DayOfWeek dayOfWeek);

  List<Appointment> findAppointmentsByMedicalCenterIdAndProfessionalIdAndMonth(
      Integer medicalCenterId, Integer professionalId, java.time.Month month);

  Optional<Appointment> findMaybeById(Integer appointmentId);

  Appointment saveAppointment(Appointment appointment);

  Set<Appointment> findAppointmentsByMedicalCenterIdAndMonth(Integer medicalCenterId, Month month);

  void changeAppointmentConsultations(
      Appointment appointment, List<ConsultationType> consultationTypeStream);
}
