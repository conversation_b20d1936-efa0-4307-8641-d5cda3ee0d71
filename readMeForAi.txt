APPLICATION LAYER CLASSES - TURNERA CODEBASE

This document lists all Java classes found in application layer packages (com.turnera.turnera.*.application) organized by module:

=== APPOINTMENT MODULE ===
AppointmentValidations : Validates appointment existence and retrieves appointments by ID for business operations
FindAppointmentById : Finds appointments by ID, returning Optional for safe null handling
FindAppointmentsForMedicalCenterAndConsultationTypeForDate : Retrieves appointments for specific medical center, consultation type, and date
FindAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek : Finds appointments by medical center, professional, and day of week
FindAppointmentsForMedicalCenterAndProfessionalForDate : Retrieves appointments for specific medical center, professional, and date
FindAppointmentsForMedicalCenterAndProfessionalForMonth : Finds monthly appointments for medical center and professional combinations
FindAppointmentsForMedicalCenterInMonth : Retrieves all appointments for a medical center within a specific month
FindFutureAppointmentsForMedicalCenterAndProfessional : Finds upcoming appointments for medical center and professional combinations
MakeAppointment : Creates new appointments with comprehensive validation including professional relationships and schedule conflicts
MigrateAppointments : Migrates appointment data from old patient IDs to new patient IDs during patient consolidation
ModifyAppointment : Updates existing appointment details including professional, consultation types, date, time, and price

=== BLOCKED SLOT MODULE ===
CreateBlockedSlot : Creates blocked time slots for professionals, validating against existing appointments and schedules
FindBlockedSlotsByProfessionalAndMedicalCenterAndDate : Finds blocked slots for professional-medical center combinations on specific dates
FindBlockedSlotsForMedicalCenterAndProfessionalForMonth : Retrieves monthly blocked slots for professional-medical center pairs
FindBlockedSlotsForMedicalCenterInMonth : Finds all blocked slots for a medical center within a specific month

=== CONSULTATION TYPE MODULE ===
ConsultationTypeValidations : Validates consultation type relationships, patient limits, and cooldown periods for appointments
FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter : Retrieves consultation type relationships for professional-medical center pairs

=== EMPLOYEE USER MODULE ===
EmployeeUserValidations : Validates employee user existence and permissions for medical center operations
FindEmployeeUserByAuth0Id : Finds employee users by their Auth0 authentication ID
FindEmployeeUserById : Retrieves employee users by their internal ID
FindMaybeEmployeeUserByAuth0Id : Safely finds employee users by Auth0 ID, returning Optional
GetEmployeeUserMedicalCenters : Retrieves all medical centers associated with a specific employee user

=== HEALTH INSURANCE MODULE ===
AssociateHealthInsurancesToMedicalCenter : Associates multiple health insurances to a medical center through employee user requests
AssociateHealthInsurancesToProfessionalInMedicalCenter : Associates health insurances to professionals within specific medical centers
FindHealthInsurance : Finds health insurance entities by their ID
FindHealthInsurancesInMedicalCenter : Retrieves all health insurances associated with a specific medical center
HealthInsuranceValidations : Validates health insurance existence and relationships

=== MEDICAL CENTER MODULE ===
CreateMedicalCenter : Creates new medical center entities with provided input data
FindGeneralInformation : Retrieves general information about medical centers including address, contact, and schedule details
FindMedicalCenterById : Retrieves medical center entities by their ID
FindMedicalCenterPatients : Finds all patients associated with a specific medical center with appointment history
FindMultipleProfessionalAgendas : Retrieves agenda information for multiple professionals within a medical center for a specific month
FindProfessionalAgenda : Retrieves detailed agenda information for a specific professional in a medical center
MedicalCenterValidations : Validates medical center existence for business operations

=== PATIENT MODULE ===
AssociateHealthInsuranceToPatient : Associates health insurance to patient entities with user tracking and creator type
AssociateMedicalCenter : Associates medical centers to patients with user tracking and creator type information
CreatePatient : Creates new patient entities and associates them with medical centers through employee users
CreatePatientForUser : Creates patient entities for turnera users and migrates existing appointment data
FindPatientById : Finds patient entities by their ID from the domain service
FindPatientByUserIdAndIdentificationNumber : Finds patient entities by user ID and identification number combination
PatientValidations : Validates patient existence and medical center relationships
UpdateMedicalCenterRelationshipByAppointment : Updates patient-medical center relationships based on appointment data

=== PROFESSIONAL MODULE ===
CreateProfessionalMedicalCenterRelationship : Creates relationships between professionals and medical centers with health insurance associations
FindMaybeProfessionalByAuth0Id : Safely retrieves professionals by Auth0 ID, returning Optional for null safety
FindProfessionalById : Finds professional entities by their ID
FindProfessionalByMedicalLicense : Retrieves professionals by their medical license number
FindProfessionalMedicalCenterRelationshipById : Finds professional-medical center relationship entities by ID
ProfessionalDetailService : Provides detailed professional information including consultation types and schedule data
ProfessionalValidations : Validates professional existence, medical center relationships, and professional-specific business rules

=== SCHEDULE MODULE ===
CreateAppointmentSchedules : Creates multiple appointment schedule entities in batch operations
CreateMedicalCenterSchedule : Creates medical center schedule entities for specific days of the week
DeleteAppointmentSchedules : Deletes multiple appointment schedule entities in batch operations
DeleteMedicalCenterSchedules : Deletes multiple medical center schedule entities in batch operations
DeleteSpecialSchedule : Deletes special schedule entries after validating no conflicting appointments exist
DeleteVacationSchedule : Deletes vacation schedule entries with medical center ownership validation
FindScheduleInfoForProfessionalAndMedicalCenter : Retrieves schedule information for professional-medical center combinations
ModifyAppointmentSchedules : Updates multiple appointment schedule entities with new time and configuration data
ModifyMedicalCenterSchedule : Updates medical center schedule entities with new day and time information
SetAppointmentSchedule : Orchestrates appointment schedule creation, modification, and deletion for specific days of the week
SetMedicalCenterSchedule : Orchestrates medical center schedule creation, modification, and deletion operations
SetSpecialSchedule : Creates special schedule entries with collision validation against existing schedules
SetVacationSchedule : Creates vacation schedules for professionals after validating against existing appointments
ValidateAgendaForSlotCreation : Validates appointment slot creation against existing schedules, appointments, and blocked slots

=== TURNERA USER MODULE ===
FindActiveTurneraUserById : Retrieves active turnera users by their ID from the domain service
FindMaybeTurneraUserByAuth0Id : Safely finds turnera users by Auth0 ID, returning Optional
UserValidations : Validates turnera user existence and active status

=== USER MODULE ===
FindUserInformationByAuth0Id : Aggregates user information from multiple user types (turnera, employee, professional) by Auth0 ID