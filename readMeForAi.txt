APPLICATION LAYER CLASSES - TURNERA CODEBASE

This document lists all Java classes found in application layer packages (com.turnera.turnera.*.application) with their primary responsibilities:

AppointmentValidations : Validates appointment existence and retrieves appointments by ID for business operations
AssociateHealthInsurancesToMedicalCenter : Associates multiple health insurances to a medical center through employee user requests
AssociateHealthInsurancesToProfessionalInMedicalCenter : Associates health insurances to professionals within specific medical centers
ConsultationTypeValidations : Validates consultation type relationships, patient limits, and cooldown periods for appointments
CreateBlockedSlot : Creates blocked time slots for professionals, validating against existing appointments and schedules
CreateMedicalCenter : Creates new medical center entities with provided input data
CreatePatient : Creates new patient entities and associates them with medical centers through employee users
CreatePatientForUser : Creates patient entities for turnera users and migrates existing appointment data
CreateProfessionalMedicalCenterRelationship : Creates relationships between professionals and medical centers with health insurance associations
DeleteSpecialSchedule : Deletes special schedule entries after validating no conflicting appointments exist
EmployeeUserValidations : Validates employee user existence and permissions for medical center operations
FindActiveTurneraUserById : Retrieves active turnera users by their ID from the domain service
FindAppointmentById : Finds appointments by ID, returning Optional for safe null handling
FindAppointmentsForMedicalCenterAndConsultationTypeForDate : Retrieves appointments for specific medical center, consultation type, and date
FindAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek : Finds appointments by medical center, professional, and day of week
FindAppointmentsForMedicalCenterAndProfessionalForDate : Retrieves appointments for specific medical center, professional, and date
FindAppointmentsForMedicalCenterAndProfessionalForMonth : Finds monthly appointments for medical center and professional combinations
FindAppointmentsForMedicalCenterInMonth : Retrieves all appointments for a medical center within a specific month
FindBlockedSlotsByProfessionalAndMedicalCenterAndDate : Finds blocked slots for professional-medical center combinations on specific dates
FindBlockedSlotsForMedicalCenterAndProfessionalForMonth : Retrieves monthly blocked slots for professional-medical center pairs
FindBlockedSlotsForMedicalCenterInMonth : Finds all blocked slots for a medical center within a specific month
FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter : Retrieves consultation type relationships for professional-medical center pairs
FindEmployeeUserByAuth0Id : Finds employee users by their Auth0 authentication ID
FindEmployeeUserById : Retrieves employee users by their internal ID
FindFutureAppointmentsForMedicalCenterAndProfessional : Finds upcoming appointments for medical center and professional combinations
FindGeneralInformation : Retrieves general information about medical centers including address, contact, and schedule details
FindHealthInsurance : Finds health insurance entities by their ID
FindHealthInsurancesInMedicalCenter : Retrieves all health insurances associated with a specific medical center
FindMaybeEmployeeUserByAuth0Id : Safely finds employee users by Auth0 ID, returning Optional
FindMaybeProfessionalByAuth0Id : Safely retrieves professionals by Auth0 ID, returning Optional for null safety
FindMaybeTurneraUserByAuth0Id : Safely finds turnera users by Auth0 ID, returning Optional
FindMedicalCenterById : Retrieves medical center entities by their ID
FindMedicalCenterPatients : Finds all patients associated with a specific medical center with appointment history
FindMultipleProfessionalAgendas : Retrieves agenda information for multiple professionals within a medical center for a specific month
FindPatientById : Finds patient entities by their ID from the domain service
FindProfessionalAgenda : Retrieves detailed agenda information for a specific professional in a medical center
FindProfessionalById : Finds professional entities by their ID
FindProfessionalByMedicalLicense : Retrieves professionals by their medical license number
FindProfessionalMedicalCenterRelationshipById : Finds professional-medical center relationship entities by ID
FindScheduleInfoForProfessionalAndMedicalCenter : Retrieves schedule information for professional-medical center combinations
FindUserInformationByAuth0Id : Aggregates user information from multiple user types (turnera, employee, professional) by Auth0 ID
GetEmployeeUserMedicalCenters : Retrieves all medical centers associated with a specific employee user
HealthInsuranceValidations : Validates health insurance existence and relationships
MakeAppointment : Creates new appointments with comprehensive validation including professional relationships and schedule conflicts
MedicalCenterValidations : Validates medical center existence for business operations
MigrateAppointments : Migrates appointment data from old patient IDs to new patient IDs during patient consolidation
ModifyAppointment : Updates existing appointment details including professional, consultation types, date, time, and price
PatientValidations : Validates patient existence and medical center relationships
ProfessionalDetailService : Provides detailed professional information including consultation types and schedule data
ProfessionalValidations : Validates professional existence, medical center relationships, and professional-specific business rules
SetVacationSchedule : Creates vacation schedules for professionals after validating against existing appointments
UpdateMedicalCenterRelationshipByAppointment : Updates patient-medical center relationships based on appointment data
UserValidations : Validates turnera user existence and active status
ValidateAgendaForSlotCreation : Validates appointment slot creation against existing schedules, appointments, and blocked slots